import { describe, it, expect } from "vitest";
import { parseEnv, isProd, isDev } from "../../src/utils/env";

describe("env helpers", () => {
  describe("isProd", () => {
    it("should return true for 'production'", () => {
      expect(isProd("production")).toBe(true);
    });

    it("should return false for other modes", () => {
      expect(isProd("development")).toBe(false);
    });
  });

  describe("isDev", () => {
    it("should return true for 'development'", () => {
      expect(isDev("development")).toBe(true);
    });

    it("should return false for other modes", () => {
      expect(isDev("production")).toBe(false);
    });
  });

  describe("parseEnv", () => {
    it("should parse env vars correctly", () => {
      const env = parseEnv({
        BASE_URL: "https://example.com",
        NODE_ENV: "development",
        VITE_SENTRY_ORG: "sentry-org",
        VITE_SENTRY_PROJECT: "sentry-project",
        VITE_SENTRY_AUTH_TOKEN: "token",
        VITE_SENTRY_TELEMETRY: "true",
        VITE_SENTRY_IS_USE: "true",
        VITE_SENTRY_DSN: "https://dsn.io",
      });

      expect(env).toEqual({
        BASE_URL: "https://example.com",
        DEV: true,
        MODE: "development",
        PROD: false,
        SSR: false,
        VITE_SENTRY_ORG: "sentry-org",
        VITE_SENTRY_PROJECT: "sentry-project",
        VITE_SENTRY_AUTH_TOKEN: "token",
        VITE_SENTRY_TELEMETRY: true,
        VITE_SENTRY_IS_USE: true,
        VITE_SENTRY_DSN: "https://dsn.io",
      });
    });

    it("should handle missing optional values and falsey booleans", () => {
      const env = parseEnv({
        BASE_URL: "http://localhost",
        NODE_ENV: "production",
        VITE_SENTRY_ORG: "",
        VITE_SENTRY_PROJECT: "",
        VITE_SENTRY_AUTH_TOKEN: "",
        VITE_SENTRY_TELEMETRY: "false",
        VITE_SENTRY_IS_USE: "false",
        VITE_SENTRY_DSN: "",
      });

      expect(env).toEqual({
        BASE_URL: "http://localhost",
        DEV: false,
        MODE: "production",
        PROD: true,
        SSR: false,
        VITE_SENTRY_ORG: "",
        VITE_SENTRY_PROJECT: "",
        VITE_SENTRY_AUTH_TOKEN: "",
        VITE_SENTRY_TELEMETRY: false,
        VITE_SENTRY_IS_USE: false,
        VITE_SENTRY_DSN: "",
      });
    });
  });
});