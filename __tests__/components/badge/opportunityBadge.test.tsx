import { render, screen } from "@testing-library/react";
import { describe, it, expect } from "vitest";
import {
  OpportunityBadge,
  type OpportunityType,
} from "../../../src/components/badge/opportunityBadge";

describe("OpportunityBadge", () => {
  it("should render nothing when no type is provided", () => {
    const { container } = render(<OpportunityBadge />);
    expect(container.firstChild).toBeNull();
  });

  it("should render hot badge correctly", () => {
    render(<OpportunityBadge type="hot" />);

    expect(screen.getByText("Hot")).toBeInTheDocument();
    expect(screen.getByAltText("Sun")).toBeInTheDocument();
    expect(screen.getByText("Hot")).toHaveClass("text-error-content");
  });

  it("should render warm badge correctly", () => {
    render(<OpportunityBadge type="warm" />);

    expect(screen.getByText("Warm")).toBeInTheDocument();
    expect(screen.getByAltText("Moon")).toBeInTheDocument();
    expect(screen.getByText("Warm")).toHaveClass("text-warning-content");
  });

  it("should render cold badge correctly", () => {
    render(<OpportunityBadge type="cold" />);

    expect(screen.getByText("Cold")).toBeInTheDocument();
    expect(screen.getByAltText("Cloudy")).toBeInTheDocument();
    expect(screen.getByText("Cold")).toHaveClass("text-primary");
  });

  it("should render all badge types when showAllTypes is true", () => {
    render(<OpportunityBadge showAllTypes />);

    expect(screen.getByText("Hot")).toBeInTheDocument();
    expect(screen.getByText("Warm")).toBeInTheDocument();
    expect(screen.getByText("Cold")).toBeInTheDocument();

    expect(screen.getByAltText("Sun")).toBeInTheDocument();
    expect(screen.getByAltText("Moon")).toBeInTheDocument();
    expect(screen.getByAltText("Cloudy")).toBeInTheDocument();
  });

  it("should apply custom className", () => {
    const { container } = render(
      <OpportunityBadge type="hot" className="custom-class" />
    );

    expect(container.firstChild).toHaveClass("custom-class");
  });

  it("should apply custom className to container when showAllTypes is true", () => {
    const { container } = render(
      <OpportunityBadge showAllTypes className="custom-container" />
    );

    expect(container.firstChild).toHaveClass("custom-container");
  });

  it("should have correct base classes for all badge types", () => {
    const types: OpportunityType[] = ["hot", "warm", "cold"];

    types.forEach((type) => {
      const { container } = render(<OpportunityBadge type={type} />);
      const badge = container.firstChild as HTMLElement;

      expect(badge).toHaveClass(
        "flex",
        "w-fit",
        "items-center",
        "gap-1",
        "rounded-lg",
        "border",
        "px-2",
        "py-0.5"
      );
    });
  });
});
