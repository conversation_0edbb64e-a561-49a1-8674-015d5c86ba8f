import moon from "@assets/moon.png";
import sun from "@assets/sun.png";
import type { IconProp } from "@fortawesome/fontawesome-svg-core";
import { faChevronUp } from "@fortawesome/free-solid-svg-icons";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { cn } from "@utils/cn";

export type ChanneType = "facebook" | "line" | "instagram" | "tiktok";

interface ChannelBadgeConfig {
  type: ChanneType;
  label: string;
  icon: IconProp | string;
  iconAlt: string;
  containerClasses: string;
  textClasses: string;
  isImageIcon?: boolean;
}

const OPPORTUNITY_BADGE_CONFIG: Record<ChanneType, ChannelBadgeConfig> = {
  facebook: {
    containerClasses: "border-primary bg-success-content",
    icon: faChevronUp,
    iconAlt: "Cloudy",
    isImageIcon: false,
    label: "COLD",
    textClasses: "text-primary",
    type: "facebook",
  },
  instagram: {
    containerClasses: "border-warning-content bg-accent/30",
    icon: moon,
    iconAlt: "Moon",
    isImageIcon: true,
    label: "WARM",
    textClasses: "text-warning-content",
    type: "instagram",
  },
  line: {
    containerClasses: "border-secondary bg-secondary-content/30",
    icon: sun,
    iconAlt: "Sun",
    isImageIcon: true,
    label: "HOT",
    textClasses: "text-error-content",
    type: "line",
  },
  tiktok: {
    containerClasses: "border-warning-content bg-accent/30",
    icon: moon,
    iconAlt: "Moon",
    isImageIcon: true,
    label: "WARM",
    textClasses: "text-warning-content",
    type: "tiktok",
  },
};

const BASE_BADGE_CLASSES =
  "flex w-fit items-center gap-1 rounded-lg border px-2 py-1";
const BASE_ICON_CLASSES = "size-6";
const BASE_TEXT_CLASSES = "font-semibold text-sm";

interface ChannelBadgeProps {
  type?: ChanneType;
  className?: string;
  showAllTypes?: boolean;
}

export const OpportunityBadge = ({
  type,
  className,
  showAllTypes = false,
}: ChannelBadgeProps) => {
  if (showAllTypes) {
    return (
      <div className={cn("flex flex-wrap gap-2", className)}>
        {Object.values(OPPORTUNITY_BADGE_CONFIG).map((config) => (
          <div
            key={config.type}
            className={cn(BASE_BADGE_CLASSES, config.containerClasses)}
          >
            {config.isImageIcon ? (
              <img
                src={config.icon as string}
                alt={config.iconAlt}
                className={BASE_ICON_CLASSES}
              />
            ) : (
              <FontAwesomeIcon
                icon={config.icon as IconProp}
                className={BASE_ICON_CLASSES}
              />
            )}
          </div>
        ))}
      </div>
    );
  }

  if (!type) {
    return null;
  }

  const config = OPPORTUNITY_BADGE_CONFIG[type];

  return (
    <div className={cn(BASE_BADGE_CLASSES, config.containerClasses, className)}>
      {config.isImageIcon ? (
        <img
          src={config.icon as string}
          alt={config.iconAlt}
          className={BASE_ICON_CLASSES}
        />
      ) : (
        <FontAwesomeIcon
          icon={config.icon as IconProp}
          className={BASE_ICON_CLASSES}
        />
      )}
      <span className={cn(BASE_TEXT_CLASSES, config.textClasses)}>
        {config.label}
      </span>
    </div>
  );
};
