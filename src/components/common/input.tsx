import { cn } from "@utils/cn";

interface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  className?: string;
  icon?: React.ReactNode;
}

export const Input = ({ className, icon, ...props }: InputProps) => {
  return (
    <label
      className={cn(
        "input input-primary !outline-offset-0 h-8 w-full rounded-lg border-none bg-base-200",
        className,
      )}
    >
      {icon}
      <input required {...props} className="h-fit text-body-xs placeholder:text-color-neutral" />
    </label>
  );
};
