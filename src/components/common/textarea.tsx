import { cn } from "@utils/cn";

interface TextareaProps extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {
  className?: string;
}

export const Textarea = ({ className, ...props }: TextareaProps) => {
  return (
    <textarea
      {...props}
      className={cn(
        "textarea textarea-primary h-full rounded-lg border-none bg-base-200 text-body-xs placeholder:text-color-neutral",
        "focus:outline-none focus:ring-2 focus:ring-primary",
        className,
      )}
    />
  );
};
