import { useForm } from "@tanstack/react-form";
import type { JSX } from "react";
import { useTranslation } from "react-i18next";
import { useCounterStore } from "../stores/counter.store.ts";

export function Index(): JSX.Element {
  const { t } = useTranslation();
  const { count, increment, decrement, reset } = useCounterStore();
  const form = useForm({
    defaultValues: {
      email: "",
      name: "",
    },
    onSubmit: async ({ value }: { value: { name: string; email: string } }) => {
      alert(`Hello ${value.name}!`);
    },
  });

  return (
    <div className="h-full p-4">
      <div className="hero h-full bg-base-200">
        <div className="hero-content text-center">
          <div className="flex max-w-md flex-col gap-6">
            <h1 className="font-bold text-5xl text-blue-600">{t("common.hello")}!</h1>
            <p className="text-gray-700">
              {t("common.welcome")} to your new React app with Bun, React 19, TypeScript, TanStack
              (Router & Form), Tailwind v4, and DaisyUI v5!
            </p>
            <div className="card w-full bg-base-100 shadow-2xl">
              <form
                className="card-body"
                onSubmit={(e: React.FormEvent<HTMLFormElement>) => {
                  e.preventDefault();
                  e.stopPropagation();
                  form.handleSubmit();
                }}
              >
                <h2 className="card-title">TanStack Form Demo</h2>
                <form.Field name="name">
                  {(field) => (
                    <div className="form-control space-x-2">
                      <label className="label" htmlFor="name-input">
                        <span className="label-text">{t("forms.name")}</span>
                      </label>
                      <input
                        id="name-input"
                        className="input input-bordered"
                        name={field.name}
                        value={field.state.value}
                        onBlur={field.handleBlur}
                        onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                          field.handleChange(e.target.value)
                        }
                        placeholder={t("forms.name")}
                      />
                    </div>
                  )}
                </form.Field>
                <form.Field name="email">
                  {(field) => (
                    <div className="form-control space-x-2">
                      <label className="label" htmlFor="email-input">
                        <span className="label-text">{t("forms.email")}</span>
                      </label>
                      <input
                        id="email-input"
                        className="input input-bordered"
                        name={field.name}
                        value={field.state.value}
                        onBlur={field.handleBlur}
                        onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                          field.handleChange(e.target.value)
                        }
                        placeholder={t("forms.email")}
                        type="email"
                      />
                    </div>
                  )}
                </form.Field>
                <div className="form-control mt-6">
                  <form.Subscribe selector={(state) => [state.canSubmit, state.isSubmitting]}>
                    {([canSubmit, isSubmitting]) => (
                      <button className="btn btn-primary" type="submit" disabled={!canSubmit}>
                        {isSubmitting ? t("common.loading") : t("forms.submit")}
                      </button>
                    )}
                  </form.Subscribe>
                </div>
              </form>
            </div>
            <div className="flex flex-col items-center gap-2">
              <p className="text-xl">Count: {count}</p>
              <div className="flex gap-2">
                <button type="button" onClick={increment} className="btn btn-primary">
                  Increment
                </button>
                <button type="button" onClick={decrement} className="btn btn-secondary">
                  Decrement
                </button>
                <button type="button" onClick={reset} className="btn btn-outline">
                  Reset
                </button>
              </div>
            </div>
            <div className="mt-8" />
          </div>
        </div>
      </div>
    </div>
  );
}
