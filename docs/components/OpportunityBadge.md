# OpportunityBadge Component Optimization

## Overview

The `OpportunityBadge` component has been optimized from a static component that rendered all badge types to a flexible, reusable component that can render individual badges or all types based on props.

## Before Optimization

The original component had several issues:
- **Hardcoded**: Always rendered all three badge types (Hot, Warm, Cold)
- **Not reusable**: No way to render individual badges
- **Repetitive code**: Duplicate styling and structure for each badge
- **No type safety**: No TypeScript types for opportunity types

```tsx
// Old implementation - always rendered all badges
export const OpportunityBadge = () => {
  return (
    <>
      <div className="flex w-fit items-center gap-1 rounded-lg border border-secondary bg-secondary-content/30 px-2 py-0.5">
        <img src={sun} alt="Sun" className="size-6" />
        <span className="text-error-content text-label-sm">Hot</span>
      </div>
      {/* More hardcoded badges... */}
    </>
  );
};
```

## After Optimization

### Key Improvements

1. **Type Safety**: Added TypeScript types for opportunity types
2. **Reusable**: Can render individual badges or all types
3. **Configurable**: Centralized configuration for easy maintenance
4. **DRY Principle**: Eliminated code duplication
5. **Flexible**: Supports custom styling via className prop

### New Features

- **Individual Badge Rendering**: `<OpportunityBadge type="hot" />`
- **All Types Display**: `<OpportunityBadge showAllTypes />`
- **Custom Styling**: `<OpportunityBadge type="warm" className="custom-class" />`
- **Null Handling**: Returns null when no type is provided

## Usage Examples

### Individual Badge

```tsx
import { OpportunityBadge } from "@components/badge/opportunityBadge";

// Render a specific badge type
<OpportunityBadge type="hot" />
<OpportunityBadge type="warm" />
<OpportunityBadge type="cold" />
```

### All Badge Types (Demo/Settings)

```tsx
// Render all badge types for demo or settings pages
<OpportunityBadge showAllTypes />

// With custom container styling
<OpportunityBadge showAllTypes className="grid grid-cols-3 gap-4" />
```

### With Custom Styling

```tsx
// Individual badge with custom styling
<OpportunityBadge type="hot" className="shadow-lg hover:scale-105" />
```

### In Dropdown Options

The component integrates seamlessly with dropdown components:

```tsx
import { OpportunityBadge, type OpportunityType } from "@components/badge/opportunityBadge";

export const _OPPORTUNITY_OPTIONS = (
  ["hot", "warm", "cold"] as OpportunityType[]
).map((type) => ({
  label: <OpportunityBadge type={type} />,
  value: type,
}));
```

## API Reference

### Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `type` | `OpportunityType` | `undefined` | The type of badge to render (`"hot"`, `"warm"`, or `"cold"`) |
| `className` | `string` | `undefined` | Additional CSS classes to apply |
| `showAllTypes` | `boolean` | `false` | Whether to render all badge types |

### Types

```tsx
export type OpportunityType = "hot" | "warm" | "cold";

interface OpportunityBadgeProps {
  type?: OpportunityType;
  className?: string;
  showAllTypes?: boolean;
}
```

## Configuration

The component uses a centralized configuration object for easy maintenance:

```tsx
const OPPORTUNITY_BADGE_CONFIG: Record<OpportunityType, OpportunityBadgeConfig> = {
  hot: {
    containerClasses: "border-secondary bg-secondary-content/30",
    icon: sun,
    iconAlt: "Sun",
    label: "Hot",
    textClasses: "text-error-content",
    type: "hot",
  },
  // ... other configurations
};
```

## Testing

The component includes comprehensive tests covering:
- Individual badge rendering
- All types display
- Custom styling
- Null handling
- Correct CSS classes

Run tests with:
```bash
npm test __tests__/components/badge/opportunityBadge.test.tsx
```

## Benefits of Optimization

1. **Maintainability**: Centralized configuration makes updates easier
2. **Reusability**: Can be used in multiple contexts (dropdowns, lists, cards)
3. **Performance**: Only renders what's needed
4. **Type Safety**: Prevents runtime errors with TypeScript
5. **Consistency**: Ensures uniform styling across the application
6. **Testability**: Easier to test individual scenarios

## Migration Guide

### From Old Usage

```tsx
// Old: Always rendered all badges
<OpportunityBadge />
```

### To New Usage

```tsx
// New: Render specific badge
<OpportunityBadge type="hot" />

// New: Render all badges (equivalent to old behavior)
<OpportunityBadge showAllTypes />
```

## Future Enhancements

Potential improvements for future versions:
- Support for custom icons
- Animation transitions
- Size variants (small, medium, large)
- Custom color themes
- Internationalization support
