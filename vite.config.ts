import tailwindcss from "@tailwindcss/vite";
import react from "@vitejs/plugin-react";
import { defineConfig, loadEnv } from "vite";
import tsconfigPaths from "vite-tsconfig-paths";
import { sentryVitePlugin } from "@sentry/vite-plugin";
import { parseEnv } from "./src/utils/env";
import { isTruthyBoolean } from "./src/helpers/boolean.helper";
import { visualizer as rollupVisualizer } from "rollup-plugin-visualizer";

export default defineConfig(({ mode }) => {
  const raw = loadEnv(mode, process.cwd(), "");
  const env = parseEnv(raw);
  const plugins = [
    react(),
    tailwindcss(),
    tsconfigPaths(),
  ];

  const rollupOptions: {
    output?: { manualChunks?: Record<string, string[]> };
  } = {};
  let terserOptions = {};

  if (env.PROD) {
    terserOptions = {
      compress: {
        drop_console: true,
        drop_debugger: true,
      },
    };

    plugins.push(
      rollupVisualizer({
        open: true,
        filename: "dist/stats.html",
        gzipSize: true,
        brotliSize: true,
      })
    );

    rollupOptions.output = {
      manualChunks: {
        react: ["react", "react-dom"],
        tanstack: [
          "@tanstack/react-query",
          "@tanstack/react-router",
          "@tanstack/react-form",
        ],
        i18n: ["i18next", "react-i18next"],
        ui: ["clsx", "tailwind-merge"],
      },
    };
  }

  if (isTruthyBoolean(env.VITE_SENTRY_IS_USE) && env.PROD) {
    plugins.push(
      sentryVitePlugin({
        org: env.VITE_SENTRY_ORG,
        project: env.VITE_SENTRY_PROJECT,
        authToken: env.VITE_SENTRY_AUTH_TOKEN,
        telemetry: env.VITE_SENTRY_TELEMETRY,
      })
    );
  }

  return {
    build: {
      sourcemap: true,
      minify: env.PROD ? "terser" : undefined,
      terserOptions,
      rollupOptions,
      chunkSizeWarningLimit: 700,
    },
    plugins,
  };
});
