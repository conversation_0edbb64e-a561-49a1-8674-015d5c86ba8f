{
  "compilerOptions": {
    /* Base URL */
    "baseUrl": "./src",
    "paths": {
      "@assets/*": ["assets/*"],
      "@components/*": ["components/*"],
      "@constants/*": ["constants/*"],
      "@enums/*": ["enums/*"],
      "@pages/*": ["pages/*"],
      "@i18n/*": ["i18n/*"],
      "@schemas/*": ["schemas/*"],
      "@stores/*": ["stores/*"],
      "@utils/*": ["utils/*"],
      "@helpers/*": ["helpers/*"],
      "@icons/*": ["icons/*"],
      "@middlewares/*": ["middlewares/*"]
    },

    /* Core Configuration */
    "target": "ES2022",
    "lib": ["ES2022", "DOM", "DOM.Iterable"],
    "module": "ESNext",
    "moduleResolution": "bundler",
    "moduleDetection": "force",

    /* React & JSX */
    "jsx": "react-jsx",
    "useDefineForClassFields": true,

    /* Module Compatibility */
    "esModuleInterop": true,
    "allowSyntheticDefaultImports": true,
    "allowImportingTsExtensions": true,
    "resolveJsonModule": true,
    "isolatedModules": true,
    "verbatimModuleSyntax": true,

    /* Build & Performance */
    "noEmit": true,
    "skipLibCheck": true,
    "tsBuildInfoFile": "./node_modules/.tmp/tsconfig.tsbuildinfo",

    /* Type Checking */
    "strict": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "noFallthroughCasesInSwitch": true,
    "noUncheckedSideEffectImports": true,

    /* Vite Integration */
    "types": [
      "vite/client",
      "vitest",
      "@testing-library/jest-dom",
      "@testing-library/react"
    ]
  },
  "include": ["src/**/*", "vite.config.ts", "vite-env.d.ts"],
  "exclude": [".tanstack", "node_modules", "dist"]
}
