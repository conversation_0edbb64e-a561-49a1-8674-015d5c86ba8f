/// <reference types="vitest" />
import { defineConfig } from "vite";
import react from "@vitejs/plugin-react";
import path from "path";
import tsconfigPaths from "vite-tsconfig-paths";

export default defineConfig(() => {
  const plugins = [react(), tsconfigPaths()];
  const resolve = {
    alias: {
      "@": path.resolve(__dirname, "src"),
    },
  };

  const test = {
    globals: true,
    environment: "jsdom",
    setupFiles: "./vitest.setup.ts",
    coverage: {
      reporter: ["text", "html", "lcov", "json-summary", "json"],
      include: ["src/**/*.{ts,tsx}"],
      enabled: true,
      exclude: [
        ".cache",
        ".scannerwork",
        ".vscode",
        ".github",
        ".husky",
        "dist",
        "coverage",
        "public",
        "node_modules",
        "**/*.d.ts",
      ],
    },
  };

  return {
    plugins,
    resolve,
    test
  };
});
